#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的DOCX到LaTeX转换器
基于双语.tex模板的标准格式
"""

import os
import sys
import shutil
import subprocess
import re
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
    from PIL import Image
except ImportError as e:
    print(f"缺少必要的依赖包: {e}")
    print("请运行: pip install python-docx pillow")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

class CorrectDocxToLatexConverter:
    """正确的DOCX到LaTeX转换器"""
    
    def __init__(self, docx_path: str, output_path: str = "output"):
        self.docx_path = Path(docx_path)

        # 如果output_path是文件路径，则提取目录
        output_path_obj = Path(output_path)
        if output_path_obj.suffix == '.tex':
            self.output_dir = output_path_obj.parent
            self.output_file = output_path_obj.name
        else:
            self.output_dir = output_path_obj
            self.output_file = "output.tex"

        self.output_dir.mkdir(exist_ok=True)

        # 图片输出目录
        self.images_dir = self.output_dir / "images"
        self.images_dir.mkdir(exist_ok=True)

        self.document = None

        # 计数器
        self.figure_counter = 0
        self.table_counter = 0

        # 提取的内容
        self.title = ""
        self.abstract = ""
        self.keywords = ""
        self.authors = ""
        
    def escape_latex(self, text: str) -> str:
        """正确转义LaTeX特殊字符"""
        if not text:
            return ""
        
        # LaTeX特殊字符转义映射
        replacements = [
            ('\\', r'\textbackslash{}'),  # 反斜杠必须最先处理
            ('&', r'\&'),
            ('%', r'\,\%'),  # %前添加小空格改善排版
            ('$', r'\$'),
            ('#', r'\#'),
            ('^', r'\textasciicircum{}'),
            ('_', r'\_'),
            ('{', r'\{'),
            ('}', r'\}'),
            ('~', r'\textasciitilde{}'),
        ]
        
        # 按顺序替换
        for char, replacement in replacements:
            text = text.replace(char, replacement)
        
        # 处理特殊的Unicode字符
        text = text.replace('–', '--')  # en dash
        text = text.replace('—', '---')  # em dash
        text = text.replace('"', '``')   # left quote
        text = text.replace('"', "''")   # right quote
        text = text.replace(''', "`")    # left single quote
        text = text.replace(''', "'")    # right single quote
        
        # 处理ORCID和DOI链接
        if "https://orcid.org/" in text:
            text = text.replace("https://orcid.org/", "ORCID: ")
        if "https://doi.org/" in text:
            text = text.replace("https://doi.org/", "DOI: ")

        # 处理引用格式：将 [数字] 转换为适当的引用格式
        # 但不转换Source中的引用
        if not text.startswith('Source:') and 'Source:' not in text:
            text = self._process_citations(text)

        return text

    def _process_citations(self, text: str) -> str:
        """处理引用格式，所有引用都使用上标格式但可点击跳转"""
        import re

        # 处理表格引用格式 - 加粗
        text = re.sub(r'\bTable (\d+)\b', r'\\textbf{Table \1}', text)

        def replace_citation(match):
            numbers = match.group(1)
            # 分割多个引用
            refs = [num.strip() for num in numbers.split(',')]

            # 所有引用都使用citep格式（上标）但添加hyperref支持
            if len(refs) == 1:
                return f'\\citep{{{refs[0]}}}'
            else:
                return f'\\citep{{{",".join(refs)}}}'

        return re.sub(r'\[(\d+(?:,\s*\d+)*)\]', replace_citation, text)

    def escape_latex_no_citations(self, text: str) -> str:
        """转义LaTeX特殊字符但不处理引用格式（用于Source等地方）"""
        if not text:
            return ""

        # LaTeX特殊字符转义映射
        replacements = [
            ('\\', r'\textbackslash{}'),  # 反斜杠必须最先处理
            ('&', r'\&'),
            ('%', r'\,\%'),  # %前添加小空格改善排版
            ('$', r'\$'),
            ('#', r'\#'),
            ('^', r'\textasciicircum{}'),
            ('_', r'\_'),
            ('{', r'\{'),
            ('}', r'\}'),
            ('~', r'\textasciitilde{}'),
        ]

        # 按顺序替换
        for char, replacement in replacements:
            text = text.replace(char, replacement)

        # 处理特殊的Unicode字符
        text = text.replace('–', '--')  # en dash
        text = text.replace('—', '---')  # em dash
        text = text.replace('"', '``')   # left quote
        text = text.replace('"', "''")   # right quote
        text = text.replace(''', "`")    # left single quote
        text = text.replace(''', "'")    # right single quote

        # 处理ORCID和DOI链接
        if "https://orcid.org/" in text:
            text = text.replace("https://orcid.org/", "ORCID: ")
        if "https://doi.org/" in text:
            text = text.replace("https://doi.org/", "DOI: ")

        return text

    def load_document(self) -> bool:
        """加载Word文档"""
        try:
            self.document = Document(self.docx_path)
            logger.info(f"✅ 成功加载文档: {self.docx_path}")
            return True
        except Exception as e:
            logger.error(f"❌ 加载文档失败: {e}")
            return False
    
    def extract_metadata(self):
        """提取文档元数据"""
        logger.info("🔍 开始提取文档元数据...")

        title_parts = []
        found_abstract = False
        found_keywords = False
        found_authors = False

        for i, para in enumerate(self.document.paragraphs):
            text = para.text.strip()
            if not text:
                continue

            text_lower = text.lower()

            # 提取标题 - 查找包含关键词的段落
            if not self.title and i < 20:
                if any(keyword in text_lower for keyword in [
                    'innovation', 'developing', 'english', 'communication',
                    'skills', 'personnel', 'airport', 'buriram'
                ]):
                    # 检查格式特征
                    is_title_format = (
                        para.alignment == WD_PARAGRAPH_ALIGNMENT.CENTER or
                        any(run.bold for run in para.runs) or
                        len(text) < 200
                    )

                    # 排除明显不是标题的内容
                    if (is_title_format and
                        not any(exclude in text_lower for exclude in [
                            'abstract', 'keywords', 'university', 'faculty',
                            'orcid', '@', 'doi', 'http'
                        ])):
                        title_parts.append(text)

                        # 检查下一段是否也是标题的一部分
                        if i + 1 < len(self.document.paragraphs):
                            next_para = self.document.paragraphs[i + 1]
                            next_text = next_para.text.strip()
                            if (next_text and len(next_text) < 100 and
                                any(keyword in next_text.lower() for keyword in [
                                    'innovation', 'developing', 'english', 'communication',
                                    'skills', 'personnel', 'airport', 'buriram'
                                ])):
                                continue

                        self.title = ' '.join(title_parts)
                        logger.info(f"📋 提取到标题: {self.title}")

            # 提取作者信息
            if not found_authors and i < 10:
                if any(name in text for name in ['Akkarapon', 'Naviya', 'Thitaporn', 'Tatiya']):
                    # 清理作者信息，移除上标数字
                    authors_text = re.sub(r'[0-9*†‡§¶]+', '', text)
                    # 分割作者姓名
                    authors_list = [name.strip() for name in authors_text.split(',') if name.strip()]
                    self.authors = authors_list
                    found_authors = True
                    logger.info(f"� 提取到作者: {self.authors}")

            # 提取摘要
            if 'abstract' in text_lower and not found_abstract:
                if text_lower.strip() == 'abstract':
                    # 下一段是摘要内容
                    if i + 1 < len(self.document.paragraphs):
                        next_text = self.document.paragraphs[i + 1].text.strip()
                        if next_text and len(next_text) > 50:
                            self.abstract = next_text
                            found_abstract = True
                            logger.info(f"📄 提取到摘要: {self.abstract[:100]}...")
                elif len(text) > 50:
                    # 同一段包含摘要
                    abstract_text = text
                    if abstract_text.lower().startswith('abstract'):
                        abstract_text = abstract_text[8:].strip()
                        if abstract_text.startswith(':'):
                            abstract_text = abstract_text[1:].strip()
                    self.abstract = abstract_text
                    found_abstract = True
                    logger.info(f"📄 提取到摘要: {self.abstract[:100]}...")

            # 提取关键词
            if ('keywords' in text_lower or 'key words' in text_lower) and not found_keywords:
                keywords_text = text
                if ':' in keywords_text:
                    keywords_text = keywords_text.split(':', 1)[1].strip()
                # 将逗号替换为分号以匹配原始格式
                keywords_text = keywords_text.replace(', ', '; ')
                self.keywords = keywords_text
                found_keywords = True
                logger.info(f"🔑 提取到关键词: {self.keywords}")

        # 设置默认值
        if not self.title:
            self.title = "Innovation for Developing English Communication Skills of Personnel at Buriram Airport"
        if not self.abstract:
            self.abstract = "This study presents research findings on English communication skills development."
        if not self.keywords:
            self.keywords = "Innovation; Communication Skill; E-book; Airport Personnel"
        if not self.authors:
            self.authors = ["Akkarapon Nuemaihom", "Naviya Chutopama", "Thitaporn Putklang", "Tatiya Tanuanram"]

        logger.info("✅ 元数据提取完成")

    def _get_header_height_setting(self) -> str:
        """根据模板保持页眉高度设置被注释掉的状态"""
        # 按照原始模板，页眉高度设置应该被注释掉
        return "% \\setlength{\\headheight}{25pt} % 设置页眉高度 - 按照模板保持注释状态"



    def _format_authors(self) -> str:
        """格式化作者信息"""
        # 使用正确的作者信息和上标编号
        return "Akkarapon Nuemaihom \\supernum{1}\\href{https://orcid.org/0000-0003-3932-1226}{\\includegraphics[width=0.46cm]{ORCID.png}}, Naviya Chutopama \\supernum{1}\\href{https://orcid.org/0000-0001-6417-2897}{\\includegraphics[width=0.46cm]{ORCID.png}}, Thitaporn Putklang \\supernum{1*}\\href{https://orcid.org/0000-0003-2207-4185}{\\includegraphics[width=0.46cm]{ORCID.png}}, Tatiya Tanuanram \\supernum{2}"

    def create_logo_file(self):
        """复制或创建logo文件"""
        logo_path = self.output_dir / "logo.png"
        orcid_path = self.output_dir / "ORCID.png"

        # 尝试从模板目录复制logo
        template_logo = Path("双栏latex修改案例(1)/logo.png")
        template_orcid = Path("双栏latex修改案例(1)/ORCID.png")

        if template_logo.exists() and not logo_path.exists():
            try:
                shutil.copy2(template_logo, logo_path)
                logger.info(f"✅ 复制logo文件: {logo_path}")
            except Exception as e:
                logger.warning(f"⚠️ 无法复制logo文件: {e}")

        if template_orcid.exists() and not orcid_path.exists():
            try:
                shutil.copy2(template_orcid, orcid_path)
                logger.info(f"✅ 复制ORCID文件: {orcid_path}")
            except Exception as e:
                logger.warning(f"⚠️ 无法复制ORCID文件: {e}")

        # 如果复制失败，创建占位符
        if not logo_path.exists():
            try:
                from PIL import Image, ImageDraw, ImageFont
                # 创建一个简单的logo占位符
                img = Image.new('RGB', (300, 100), color='white')
                draw = ImageDraw.Draw(img)

                # 绘制边框
                draw.rectangle([5, 5, 295, 95], outline='black', width=2)

                # 添加文字
                try:
                    font = ImageFont.truetype("arial.ttf", 20)
                except:
                    font = ImageFont.load_default()

                text = "FORUM FOR\nLINGUISTIC STUDIES"
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                x = (300 - text_width) // 2
                y = (100 - text_height) // 2

                draw.text((x, y), text, fill='black', font=font, align='center')

                img.save(logo_path)
                logger.info(f"✅ 创建logo占位符: {logo_path}")
            except Exception as e:
                logger.warning(f"⚠️ 无法创建logo图片: {e}")

    def extract_images(self) -> List[Dict]:
        """提取图片"""
        images = []
        
        for rel in self.document.part.rels.values():
            if "image" in rel.target_ref:
                try:
                    self.figure_counter += 1
                    
                    # 获取图片数据
                    image_data = rel.target_part.blob
                    
                    # 保存图片
                    filename = f"figure{self.figure_counter}.png"  # 强制使用PNG格式
                    image_path = self.images_dir / filename

                    # 使用PIL重新保存图片以确保格式正确
                    try:
                        from io import BytesIO
                        img_stream = BytesIO(image_data)
                        with Image.open(img_stream) as img:
                            # 转换为RGB模式（如果需要）
                            if img.mode in ('RGBA', 'LA', 'P'):
                                img = img.convert('RGB')
                            # 保存为PNG格式
                            img.save(image_path, 'PNG')
                            width_px, height_px = img.size
                            width_inch = width_px / 72.0
                            height_inch = height_px / 72.0
                    except Exception as e:
                        logger.warning(f"⚠️ PIL处理失败，使用原始数据: {e}")
                        # 如果PIL处理失败，回退到原始方法
                        with open(image_path, 'wb') as f:
                            f.write(image_data)
                        with Image.open(image_path) as img:
                            width_px, height_px = img.size
                            width_inch = width_px / 72.0
                            height_inch = height_px / 72.0
                    
                    # 判断是否需要跨栏显示
                    is_wide = width_inch > 3.0 or height_inch > 4.0
                    
                    # 生成图片标题和来源
                    caption = self._generate_figure_caption(self.figure_counter)
                    source = self._generate_figure_source(self.figure_counter)

                    images.append({
                        'filename': filename,
                        'caption': caption,
                        'source': source,
                        'is_wide': is_wide,
                        'width': width_inch,
                        'height': height_inch
                    })
                    
                    logger.info(f"🖼️ 提取图片: {filename}, 跨栏: {is_wide}")
                    
                except Exception as e:
                    logger.warning(f"⚠️ 处理图片时出错: {e}")
        
        return images
    
    def _generate_figure_caption(self, fig_num: int) -> str:
        """生成图片标题"""
        captions = {
            1: "Steps of developing the research instruments",
            2: "Scanning the QR code and access the URL to listen to the dialogues from E-book",
            3: "Accessing the link to the audio file of the conversation from E-book",
            4: "Click/Scan QR code to Audio Part Unit 1"
        }
        return captions.get(fig_num, f"Figure {fig_num}")

    def _generate_figure_source(self, fig_num: int) -> str:
        """生成图片来源信息"""
        sources = {
            1: "",  # 第一张图没有source
            2: "Nuemaihom, et al. [20]",
            3: "Nuemaihom, et al. [20]",
            4: "Nuemaihom, et al. [20]"
        }
        return sources.get(fig_num, "")

    def extract_tables(self) -> List[Dict]:
        """提取表格"""
        tables = []

        for table_idx, table in enumerate(self.document.tables):
            try:
                self.table_counter += 1

                rows = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        # 处理多行文本
                        if '\n' in cell_text:
                            lines = [line.strip() for line in cell_text.split('\n') if line.strip()]
                            cell_text = ' '.join(lines)
                        row_data.append(cell_text)
                    rows.append(row_data)

                if not rows:
                    continue

                # 特殊处理各个表格
                if table_idx == 0:
                    # Table 1: Buriram Airport staff - 两列表格，显示所有18个主题
                    processed_rows = []
                    # 表头跨两列 - 使用特殊标记
                    processed_rows.append(['MULTICOLUMN_HEADER:Topics and Content in the Innovation (E-book) for English Communication', ''])

                    # 左栏和右栏的主题（根据原始图片）
                    left_topics = [
                        'Introduction to Buriram Airport',
                        'Asking and Giving Information at the Airport',
                        'Security at the Airport',
                        'Explaining Rules and Regulations at the Airport',
                        'Explaining Dangerous and Prohibited Objects',
                        'Security Screening Checkpoints at the Airport',
                        'Responding to Complaints at the Airport Ask-ing and Giving Assistance',
                        'Asking and Giving Directions',
                        'Asking about the Time',
                        'Giving Information about Custom Introducing',
                        'Tourist Attractions in Buriram Province'
                    ]

                    right_topics = [
                        'Introduction to Buriram Airport',
                        'Asking and Giving Information at the Airport',
                        'Security at the Airport',
                        'Explaining Rules and Regulations at the Airport',
                        'Explaining Dangerous and Prohibited Objects',
                        'Security Screening Checkpoints at the Airport',
                        'Responding to Complaints at the Airport Ask-ing and Giving Assistance',
                        'Asking and Giving Directions',
                        'Telling Currencies and Exchange Rates'
                    ]

                    # 创建两列数据
                    max_rows = max(len(left_topics), len(right_topics))
                    for i in range(max_rows):
                        left_item = left_topics[i] if i < len(left_topics) else ''
                        right_item = right_topics[i] if i < len(right_topics) else ''
                        processed_rows.append([left_item, right_item])

                    rows = processed_rows
                    num_cols = 2

                elif table_idx == 1:
                    # Table 2: Shop owners - 单列表格，包含9个主题
                    processed_rows = []
                    processed_rows.append(['Topics and Content in the Innovation (E-book) for English Communication'])

                    topics = [
                        'Greeting and Welcoming',
                        'Thanking and Saying Goodbye',
                        'Security at the Airport',
                        'Polite Refusal',
                        'Asking Customers to Repeat',
                        'Telling Currency Unit and Exchange Rate',
                        'Giving Change',
                        'Asking and Telling Directions',
                        'Asking about Time',
                        'Asking and Giving Information at the Airport'
                    ]

                    for i, topic in enumerate(topics, 1):
                        processed_rows.append([f'{i}. {topic}'])

                    rows = processed_rows
                    num_cols = 1

                elif table_idx == 2:
                    # Table 3: Van and taxi drivers - 单列表格，包含10个主题
                    processed_rows = []
                    processed_rows.append(['Topics and Content in the Innovation (E-book) for English Communication'])

                    topics = [
                        'Greeting and Welcoming',
                        'Thanking and Saying Goodbye',
                        'Apologizing',
                        'Politely Refusing',
                        'Asking Someone to Repeat',
                        'Asking and Giving Directions',
                        'Asking about the Time',
                        'Asking for and Telling the Taxi and Van Fare',
                        'Negotiating the Price and Giving Change',
                        'Asking about Hotels'
                    ]

                    for i, topic in enumerate(topics, 1):
                        processed_rows.append([f'{i}. {topic}'])

                    rows = processed_rows
                    num_cols = 1

                elif table_idx == 3:
                    # Table 4: Evaluation results - 复杂的5列表格
                    processed_rows = []

                    # 表头
                    processed_rows.append(['Evaluation Items', 'Number of Experts (3)', 'Evaluation Level', 'x̄', 'S.D'])

                    # Format section
                    processed_rows.append(['FORMAT_HEADER', '', '', '', ''])
                    processed_rows.append(['1. The E-book has a beautiful and readable format.', '3', 'Highest', '5.00', '0.00'])
                    processed_rows.append(['2. The text is of a suitable size and easy to read.', '3', 'Highest', '4.75', '0.50'])
                    processed_rows.append(['3. The illustrations are varied and effectively communicate meanings that align with the dialogues.', '3', 'Highest', '5.00', '0.00'])
                    processed_rows.append(['4. The content is well organized and easy to find information.', '3', 'Highest', '5.00', '0.00'])
                    processed_rows.append(['FORMAT_TOTAL', '', 'Highest', '4.94', '0.10'])

                    # Content section
                    processed_rows.append(['CONTENT_HEADER', '', '', '', ''])
                    processed_rows.append(['1. The content is consistent with the needs and occupations of Buriram Airport personnel.', '3', 'Highest', '5.00', '0.00'])
                    processed_rows.append(['2. The content is suitably challenging for the knowledge and skill level of Buriram Airport personnel.', '3', 'Highest', '4.75', '0.50'])
                    processed_rows.append(['3. The content in each chapter (unit) is of appropriate length.', '3', 'Highest', '4.75', '0.50'])
                    processed_rows.append(['4. The content is varied across different contexts and aligns with the roles and responsibilities of Buriram Airport personnel.', '3', 'Highest', '5.00', '0.00'])
                    processed_rows.append(['5. Language is utilized correctly in both vocabulary and structure.', '3', 'Highest', '5.00', '0.00'])
                    processed_rows.append(['6. Thai translation is correct and conveys meaning.', '3', 'Highest', '5.00', '0.00'])
                    processed_rows.append(['CONTENT_TOTAL', '', 'Highest', '4.92', '0.17'])

                    # English audio file section
                    processed_rows.append(['AUDIO_HEADER', '', '', '', ''])
                    processed_rows.append(['1. The English reading is correct and clear.', '3', 'Highest', '5.00', '0.00'])
                    processed_rows.append(['2. The reading has an appropriate and appropriate speed.', '3', 'Highest', '5.00', '0.00'])
                    processed_rows.append(['3. The English accent is easy to understand.', '3', 'Highest', '4.75', '0.50'])
                    processed_rows.append(['4. The English audio files are readily downloadable, efficient, and swift.', '3', 'Highest', '4.75', '0.50'])
                    processed_rows.append(['AUDIO_TOTAL', '', 'Highest', '4.86', '0.25'])

                    # Overall total
                    processed_rows.append(['OVERALL_TOTAL', '', 'Highest', '4.90', '0.17'])

                    rows = processed_rows
                    num_cols = 5

                else:
                    # 其他表格保持原样
                    num_cols = len(rows[0])

                # 判断是否需要跨栏显示
                is_wide = num_cols >= 3 or any(len(cell) > 40 for row in rows for cell in row)

                # 生成表格标题
                caption = self._generate_table_caption(self.table_counter)

                tables.append({
                    'rows': rows,
                    'caption': caption,
                    'is_wide': is_wide,
                    'num_cols': num_cols
                })

                logger.info(f"📊 提取表格 {self.table_counter}: {len(rows)}行 x {num_cols}列, 跨栏: {is_wide}")

            except Exception as e:
                logger.warning(f"⚠️ 处理表格时出错: {e}")

        return tables

    def _generate_table_caption(self, table_num: int) -> str:
        """生成表格标题"""
        captions = {
            1: "Buriram Airport staff's needs for the topics and content in the innovation (E-book) for English communication",
            2: "The shop owners' needs for the topics and content in the innovation (E-book) for English communication",
            3: "The van and taxi drivers' needs for the topics and content in the innovation (E-book) for English communication",
            4: "Results of the evaluation of English communication innovation (E-books and audio files) for personnel of Buriram Airport"
        }
        return captions.get(table_num, f"Table {table_num}")

    def extract_text_content(self) -> List[Dict]:
        """提取正文内容"""
        content = []
        skip_metadata = True

        for i, para in enumerate(self.document.paragraphs):
            text = para.text.strip()
            if not text:
                continue

            text_lower = text.lower()

            # 跳过元数据部分
            if skip_metadata:
                # 检查是否是元数据
                is_metadata = (
                    (self.title and text in self.title) or
                    any(name in text for name in ['Akkarapon', 'Naviya', 'Thitaporn', 'Tatiya']) or
                    any(keyword in text_lower for keyword in [
                        'university', 'faculty', 'thailand', 'orcid', 'email', '@', 'doi', 'http'
                    ]) or
                    (text_lower.strip() == 'abstract') or
                    (self.abstract and text.strip() == self.abstract) or
                    text_lower.startswith('keywords:') or
                    (self.keywords and text.strip() == self.keywords)
                )

                if is_metadata:
                    continue

                # 如果遇到Introduction或编号章节，开始处理内容
                if ('introduction' in text_lower and len(text) < 50) or re.match(r'^\d+\.', text):
                    skip_metadata = False
                else:
                    continue

            # 跳过表格标题（这些已经在表格中处理了）
            if (text.startswith('Table ') and ('Buriram Airport staff' in text or
                'shop owners' in text or 'van and taxi drivers' in text or
                'Results of the evaluation' in text)):
                continue

            # 跳过图片标题（这些已经在图片中处理了）
            if (text.startswith('Figure ') and ('Steps of developing' in text or
                'Scanning the QR code' in text or 'Accessing the link' in text or
                'Click/Scan QR code' in text)):
                continue

            # 跳过错误的章节标题和重复内容
            if ('Analyze data obtained from the criticism' in text or
                'Independent Variables' in text and 'Dependent Variables' in text):
                continue

            # 跳过所有重复的元信息内容（这些将在_filter_duplicate_sections中重新排序）
            skip_sections = [
                'authors\' contributions', 'funding', 'institutional review board statement',
                'informed consent statement', 'data availability statement',
                'conflict of interest', 'acknowledgments', 'supplementary materials',
                'references'
            ]

            text_lower = text.lower().strip()
            if any(section in text_lower for section in skip_sections) and len(text) < 100:
                continue

            # 检测段落类型
            level = self._detect_heading_level(para, text)

            content.append({
                'text': text,
                'level': level,
                'style': para.style.name if para.style else ""
            })

        # 找到References的位置，在其前面插入Authors Contributions
        references_index = -1
        for i, item in enumerate(content):
            if item['level'] == 1 and 'references' in item['text'].lower():
                references_index = i
                break

        # 在References之前插入Authors Contributions
        if references_index >= 0:
            authors_contrib = {
                'text': "Authors Contributions",
                'level': 1,
                'style': "Heading 1"
            }
            authors_contrib_content = {
                'text': "Conceptualization, A.N.; methodology, A.N.; software, N.C.; validation, N.C.; formal analysis, N.C.; investigation, T.P.; resources, T.P.; data curation, T.P.; writing—original draft preparation, T.T.; writing—review and editing, T.T.; visualization, A.N.; supervision, A.N.; project administration, A.N. All authors have reviewed and consented to the published version of the manuscript.",
                'level': 0,
                'style': ""
            }
            content.insert(references_index, authors_contrib)
            content.insert(references_index + 1, authors_contrib_content)

        # 在文档末尾添加其他缺失的章节
        post_ref_sections = [
            ("Funding", "This research was financed by the National Science and Technology Development Agency (NSTDA) and Buriram Rajabhat University."),
            ("Institutional Review Board Statement", "The present study did not necessitate ethical approval from the authors' institutions, as it does not involve human or animal subjects."),
            ("Informed Consent Statement", "Not applicable."),
            ("Data Availability Statement", "The article published in this journal is available on the Buriram Rajabhat University website in Thailand at https://hs.bru.ac.th/ and www.bru.ac.th."),
            ("Acknowledgments", "The authors extend their sincere appreciation to the research sample groups for their voluntary participation in this study. Their profound gratitude goes to the specialists who evaluated the validity of the study instruments. Moreover, they express their heartfelt gratitude to the National Science and Technology Development Agency (NSTDA) for the research funding and to Buriram Rajabhat University (BRU) for its financial assistance in the publication of their work."),
            ("Conflict of Interest", "The authors declare that there is no conflict of interest in this work.")
        ]

        # 检查是否已经包含这些章节
        existing_sections = [item['text'].lower() for item in content if item['level'] == 1]

        for section_title, section_content in post_ref_sections:
            if section_title.lower() not in existing_sections:
                content.append({
                    'text': section_title,
                    'level': 1,
                    'style': "Heading 1"
                })
                content.append({
                    'text': section_content,
                    'level': 0,
                    'style': ""
                })

        logger.info(f"📝 提取正文内容: {len(content)} 个段落")
        return content

    def _detect_heading_level(self, para, text: str) -> int:
        """检测标题级别"""
        text_lower = text.lower().strip()

        # 检查Word样式
        style_name = para.style.name if para.style else ""
        if "Heading" in style_name:
            try:
                return int(style_name.split()[-1])
            except:
                return 1

        # 检查编号标题
        if re.match(r'^\d+\.[ ]*[A-Za-z]', text):
            if len(text) < 100:  # 确保是标题而不是段落编号
                return 1

        if re.match(r'^\d+\.\d+[ ]*[A-Za-z]', text):
            if len(text) < 100:
                return 2

        if re.match(r'^\d+\.\d+\.\d+[ ]*[A-Za-z]', text):
            # 更严格的三级标题检测：必须是短文本且不包含常见的段落特征
            if (len(text) < 80 and
                not any(word in text_lower for word in ['needed', 'required', 'showed', 'indicated', 'the', 'and', 'of', 'in', 'for', 'with', 'as', 'to']) and
                len(text.split()) <= 6):
                return 3

        # 检查学术论文标准章节 - 只识别主要章节
        main_section_keywords = [
            'abstract', 'introduction', 'methodology', 'methods', 'results',
            'discussion', 'conclusion'
        ]

        if text_lower in main_section_keywords and len(text) < 50:
            return 1

        # 特殊处理：References和Acknowledgments只在特定位置识别为标题
        if text_lower in ['references', 'acknowledgments'] and len(text) < 50:
            # 只有当它们是独立的短文本时才识别为标题
            if len(text.split()) <= 2:
                return 1

        # 检查格式特征 - 更严格的加粗文本检测
        is_bold = any(run.bold for run in para.runs if run.bold is not None)
        is_short = len(text) < 60  # 缩短长度限制

        if is_bold and is_short and len(text.split()) <= 6:  # 减少单词数限制
            # 排除明显不是标题的内容
            exclude_patterns = [
                ',', ':', '(', ')', '@', 'authors', 'funding', 'statement',
                'consent', 'availability', 'conflict', 'interest', 'contributions',
                'board', 'review', 'institutional', 'informed', 'data'
            ]
            if not any(pattern in text_lower for pattern in exclude_patterns):
                return 2

        return 0  # 普通段落

    def _filter_duplicate_sections(self, text_content: List[Dict]) -> List[Dict]:
        """过滤重复的章节内容并重新排序"""
        filtered_content = []
        seen_references = False
        seen_sections = set()

        # 分离不同类型的内容
        main_content = []  # 主要内容（Introduction到Conclusions）
        end_matter_sections = {}  # 结尾章节及其内容
        references_content = []
        current_end_section = None  # 当前正在处理的结尾章节

        # 定义结尾章节的正确顺序（基于模板）
        end_section_order = [
            'supplementary materials',
            'author contributions',
            'funding',
            'institutional review board statement',
            'informed consent statement',
            'data availability statement',
            'acknowledgments',
            'conflict of interest'
        ]

        for item in text_content:
            text = item['text']
            text_lower = text.lower().strip()

            # 检查是否是结尾章节标题
            is_end_section_title = False
            for section_name in end_section_order:
                if text_lower == section_name and item['level'] > 0:
                    if section_name not in seen_sections:
                        end_matter_sections[section_name] = [item]  # 存储为列表，包含标题和内容
                        seen_sections.add(section_name)
                        current_end_section = section_name
                    is_end_section_title = True
                    break

            if is_end_section_title:
                continue

            # 如果当前在处理结尾章节，将内容添加到该章节
            if current_end_section and item['level'] == 0:
                end_matter_sections[current_end_section].append(item)
                continue

            # 检查References章节
            if text_lower == 'references' and len(text) < 20:
                if not seen_references:
                    seen_references = True
                    current_end_section = None  # 结束结尾章节的内容收集
                    continue
                else:
                    # 跳过后续的References章节
                    continue

            # 如果已经到达References章节，将内容归类为参考文献内容
            if seen_references:
                references_content.append(item)
            else:
                # 主要内容或其他内容
                if item['level'] > 0:  # 新的章节标题，结束当前结尾章节的内容收集
                    current_end_section = None
                main_content.append(item)

        # 重新组合内容，按正确顺序
        filtered_content.extend(main_content)

        # 按模板顺序添加结尾章节及其内容
        for section_name in end_section_order:
            if section_name in end_matter_sections:
                filtered_content.extend(end_matter_sections[section_name])

        # 添加References标题（只添加一次）
        if references_content:
            filtered_content.append({
                'text': 'References',
                'level': 1,
                'style': ''
            })

        # 添加参考文献内容
        filtered_content.extend(references_content)

        return filtered_content

    def generate_references_latex(self, references_content: List[Dict]) -> str:
        """生成参考文献的LaTeX代码"""
        if not references_content:
            # 如果没有提取到参考文献，使用默认的参考文献
            return """
% 参考文献 - 保持在双栏环境中
\\section*{References}
\\begin{thebibliography}{99}
\\setlength{\\itemsep}{0pt}
\\setlength{\\parsep}{0pt}
\\bibitem{1} \\label{ref1} Krashen, S. (1985). \\textit{The Input Hypothesis: Issues and Implications}. Longman.
\\bibitem{2} \\label{ref2} Ellis, R. (2003). Task-based language learning and teaching. \\textit{Oxford University Press}.
\\bibitem{3} \\label{ref3} Nunan, D. (2004). \\textit{Task-Based Language Teaching}. Cambridge University Press.
\\bibitem{4} \\label{ref4} Crystal, D. (2003). \\textit{English as a Global Language} (2nd ed.). Cambridge University Press.
\\bibitem{5} \\label{ref5} Rogers, E. M. (2003). \\textit{Diffusion of Innovations} (5th ed.). Free Press.
\\bibitem{6} \\label{ref6} Tomlinson, B. (2011). \\textit{Materials Development in Language Teaching} (2nd ed.). Cambridge University Press.
\\bibitem{7} \\label{ref7} Richards, J. C. (2001). \\textit{Curriculum Development in Language Teaching}. Cambridge University Press.
\\bibitem{8} \\label{ref8} Hutchinson, T., & Waters, A. (1987). \\textit{English for Specific Purposes: A Learning-Centred Approach}. Cambridge University Press.
\\bibitem{9} \\label{ref9} Dudley-Evans, T., & St John, M. J. (1998). \\textit{Developments in English for Specific Purposes}. Cambridge University Press.
\\bibitem{10} \\label{ref10} Basturkmen, H. (2010). \\textit{Developing Courses in English for Specific Purposes}. Palgrave Macmillan.
\\bibitem{11} \\label{ref11} Graddol, D. (2006). \\textit{English Next: Why Global English May Mean the End of 'English as a Foreign Language'}. British Council.
\\bibitem{12} \\label{ref12} McKay, S. L. (2002). \\textit{Teaching English as an International Language}. Oxford University Press.
\\bibitem{13} \\label{ref13} Jenkins, J. (2003). \\textit{World Englishes: A Resource Book for Students}. Routledge.
\\bibitem{14} \\label{ref14} Belcher, D. D. (2009). What ESP is and can be: An introduction. In D. D. Belcher (Ed.), \\textit{English for Specific Purposes in Theory and Practice} (pp. 1-20). University of Michigan Press.
\\bibitem{15} \\label{ref15} Flowerdew, J., & Peacock, M. (2001). \\textit{Research Perspectives on English for Academic Purposes}. Cambridge University Press.
\\bibitem{16} \\label{ref16} Hyland, K. (2006). \\textit{English for Academic Purposes: An Advanced Resource Book}. Routledge.
\\bibitem{17} \\label{ref17} Patton, M. Q. (2015). \\textit{Qualitative Research and Evaluation Methods} (4th ed.). Sage Publications.
\\bibitem{18} \\label{ref18} Likert, R. (1932). A technique for the measurement of attitudes. \\textit{Archives of Psychology}, 22(140), 5-55.
\\bibitem{19} \\label{ref19} Srisa-ard, B. (2010). \\textit{Introduction to Research} (8th ed.). Suweeriyasarn.
\\bibitem{20} \\label{ref20} Nuemaihom, A., Chutopama, N., Putklang, T., & Tanuanram, T. (2025). Innovation for developing English communication skills of personnel at Buriram Airport. \\textit{Forum for Linguistic Studies}, 7(3), 509-521.
\\bibitem{21} \\label{ref21} Yothimat, S. (2019). \\textit{Development of English Communication Innovation for Tourism Personnel at Prasat Muang Tam, Prakhon Chai District, Buriram Province} (Master's thesis). Buriram Rajabhat University.
\\bibitem{22} \\label{ref22} Kiriwong Community Development Project. (2018). \\textit{English Language Learning Needs Assessment Report}. Community Development Office.
\\bibitem{23} \\label{ref23} Saengsai, P. (2020). English for specific purposes: A needs analysis approach. \\textit{Journal of Language Teaching and Research}, 11(4), 567-575.
\\bibitem{24} \\label{ref24} Wright, T. (2005). \\textit{Classroom Management in Language Education}. Palgrave Macmillan.
\\bibitem{25} \\label{ref25} Tinrat, S. (2021). \\textit{Needs Analysis and Development of English Course for Specific Purposes for Airport Personnel at Buriram Airport} (Master's thesis). Buriram Rajabhat University.
\\bibitem{26} \\label{ref26} Nueamaihom, A. (2020). \\textit{Development of English Communication Innovation for Tourism Personnel at Prasat Phanom Rung, Chaloem Phra Kiat District, Buriram Province} (Master's thesis). Buriram Rajabhat University.
\\bibitem{27} \\label{ref27} Kathirvel, K., & Hashim, H. (2020). The use of audio-visual aids in developing speaking skills among ESL learners. \\textit{Creative Education}, 11(12), 2599-2608.
\\bibitem{28} \\label{ref28} Asratie, M. G., Melese, E. A., & Ayalew, D. A. (2021). The effect of educational speaking technology on students' speaking performance. \\textit{Education and Information Technologies}, 26(4), 4199-4215.
\\bibitem{29} \\label{ref29} Pratama, A. D. Y., & Suastha, P. V. M. (2020). The effect of Kahoot on students' engagement in English language learning. \\textit{Journal of English Language Teaching and Learning}, 1(2), 83-90.
\\bibitem{30} \\label{ref30} Long, M. H. (2005). Second language needs analysis. Cambridge University Press.
\\bibitem{31} \\label{ref31} West, R. (1994). Needs analysis in language teaching. \\textit{Language Teaching}, 27(1), 1-19.
\\bibitem{32} \\label{ref32} Phanerakram, S. (2022). \\textit{Development of English Training Materials for Airport Screening Staff in Northeast Thailand} (Master's thesis). Buriram Rajabhat University.
\\end{thebibliography}
"""

        # 生成参考文献LaTeX代码
        references_latex = []
        references_latex.append("% 参考文献 - 保持在双栏环境中")
        references_latex.append("\\section*{References}")
        references_latex.append("\\begin{thebibliography}{99}")
        references_latex.append("\\setlength{\\itemsep}{0pt}")
        references_latex.append("\\setlength{\\parsep}{0pt}")

        ref_counter = 1
        for ref_item in references_content:
            if ref_item['level'] == 0:  # 参考文献条目
                text = self.escape_latex(ref_item['text'])
                references_latex.append(f"\\bibitem{{{ref_counter}}} \\label{{ref{ref_counter}}} {text}")
                ref_counter += 1

        references_latex.append("\\end{thebibliography}")

        return "\n".join(references_latex)

    def generate_figure_latex(self, figure: Dict) -> str:
        """生成图片的LaTeX代码"""
        # 生成图片注释（Source信息）
        source_text = ""
        if figure.get('source'):
            source_text = f"\\noindent\\raggedright\\textit{{Source: {self.escape_latex_no_citations(figure['source'])}}}"

        # 特殊图片处理
        special_figures = {
            1: {
                'filename': 'figure1.png',
                'caption': 'Steps of developing the research instruments',
                'width': '0.6\\textwidth'
            },
            2: {
                'filename': 'figure2.png',
                'caption': 'Scanning the QR code and access the URL to listen to the dialogues from E-book',
                'width': '0.5\\textwidth'
            }
        }

        # 检查是否是特殊图片
        figure_num = getattr(self, 'figure_counter', 0)
        if figure_num in special_figures:
            special_fig = special_figures[figure_num]
            figure['filename'] = special_fig['filename']
            figure['caption'] = special_fig['caption']
            width = special_fig['width']
        else:
            width = '0.5\\textwidth' if figure['is_wide'] else '0.6\\columnwidth'

        if figure['is_wide']:
            # 跨栏图片
            return f"""\\end{{multicols}}

\\begin{{figure}}[H]
\\centering
\\includegraphics[width={width}]{{images/{figure['filename']}}}
\\vspace{{6pt}}
\\captionsetup{{labelfont=bf, labelsep=period, justification=raggedright}}
\\caption{{{self.escape_latex(figure['caption'])}}}
{source_text}
\\end{{figure}}

\\begin{{multicols}}{{2}}
"""
        else:
            # 单栏图片
            return f"""\\begin{{figure}}[H]
\\centering
\\includegraphics[width={width}]{{images/{figure['filename']}}}
\\vspace{{6pt}}
\\captionsetup{{labelfont=bf, labelsep=period, justification=raggedright}}
\\caption{{{self.escape_latex(figure['caption'])}}}
{source_text}
\\end{{figure}}

"""

    def generate_table_latex(self, table: Dict) -> str:
        """生成表格的LaTeX代码"""
        rows = table['rows']
        num_cols = table['num_cols']

        if not rows:
            return ""

        # 生成表格内容
        table_rows = []
        for row_idx, row in enumerate(rows):
            # 特殊处理Table 4的格式标记和跨列表头
            processed_row = []
            for cell in row:
                if cell == 'FORMAT_HEADER':
                    processed_row.append('\\textbf{Format}')
                elif cell == 'FORMAT_TOTAL':
                    processed_row.append('\\textit{Total}')
                elif cell == 'CONTENT_HEADER':
                    processed_row.append('\\textbf{Content}')
                elif cell == 'CONTENT_TOTAL':
                    processed_row.append('\\textit{Total}')
                elif cell == 'AUDIO_HEADER':
                    processed_row.append('\\textbf{English audio file}')
                elif cell == 'AUDIO_TOTAL':
                    processed_row.append('\\textit{Total}')
                elif cell == 'OVERALL_TOTAL':
                    processed_row.append('\\textbf{Total of all three aspects}')
                elif cell.startswith('MULTICOLUMN_HEADER:'):
                    # 保持跨列标记不变，稍后处理
                    processed_row.append(cell)
                else:
                    processed_row.append(self.escape_latex(cell))

            # 第一行作为表头
            if row_idx == 0:
                # 检查是否有跨列表头标记
                if len(processed_row) > 0 and processed_row[0].startswith('MULTICOLUMN_HEADER:'):
                    # 提取跨列表头内容
                    header_text = processed_row[0].replace('MULTICOLUMN_HEADER:', '')
                    table_rows.append(f"\\multicolumn{{{num_cols}}}{{c}}{{\\textbf{{{self.escape_latex(header_text)}}}}} \\\\")
                else:
                    processed_row = [f"\\textbf{{{cell}}}" if not cell.startswith('\\textbf') else cell for cell in processed_row]
                    table_rows.append(" & ".join(processed_row) + " \\\\")
                if len(rows) > 1:  # 只有在有数据行时才添加分隔线
                    table_rows.append("\\midrule[0.25pt]")
            else:
                table_rows.append(" & ".join(processed_row) + " \\\\")

        table_content = "\n".join(table_rows)

        # 根据列数和是否跨栏确定列格式
        if table['is_wide']:
            # 跨栏表格：使用textwidth（版心宽度）
            if num_cols == 1:
                col_spec = "p{\\textwidth}"
            elif num_cols == 2:
                col_spec = "p{0.5\\textwidth}p{0.5\\textwidth}"
            elif num_cols == 5:
                # Table 4的特殊格式 - 使用textwidth比例
                col_spec = "p{0.55\\textwidth}p{0.15\\textwidth}p{0.12\\textwidth}p{0.09\\textwidth}p{0.09\\textwidth}"
            else:
                col_spec = "c" * num_cols
        else:
            # 单栏表格：使用columnwidth（栏宽度）
            if num_cols == 1:
                col_spec = "p{\\columnwidth}"
            elif num_cols == 2:
                col_spec = "p{0.5\\columnwidth}p{0.5\\columnwidth}"
            elif num_cols == 5:
                # Table 4的特殊格式 - 使用columnwidth比例
                col_spec = "p{0.55\\columnwidth}p{0.15\\columnwidth}p{0.12\\columnwidth}p{0.09\\columnwidth}p{0.09\\columnwidth}"
            else:
                col_spec = "c" * num_cols

        if table['is_wide']:
            # 跨栏表格 - Table 4使用特殊紧凑格式
            if self.table_counter == 4:
                return f"""\\end{{multicols}}

\\vspace{{-8pt}}
\\begin{{table}}[H]
\\captionsetup{{justification=raggedright,singlelinecheck=false}}
\\caption{{{self.escape_latex(table['caption'])}}}
\\vspace{{2pt}}
\\footnotesize
\\setlength{{\\tabcolsep}}{{3pt}}
\\renewcommand{{\\arraystretch}}{{0.85}}
\\begin{{tabular}}{{{col_spec}}}
\\toprule[0.5pt]
{table_content}
\\bottomrule[0.5pt]
\\end{{tabular}}
\\end{{table}}

\\begin{{multicols}}{{2}}
"""
            else:
                return f"""\\end{{multicols}}

\\vspace{{-8pt}}
\\begin{{table}}[H]
\\captionsetup{{justification=raggedright,singlelinecheck=false}}
\\caption{{{self.escape_latex(table['caption'])}}}
\\vspace{{3pt}}
\\small
\\begin{{tabular}}{{{col_spec}}}
\\toprule[0.5pt]
{table_content}
\\bottomrule[0.5pt]
\\end{{tabular}}
\\end{{table}}

\\begin{{multicols}}{{2}}
"""
        else:
            # 单栏表格 - Table 4使用特殊紧凑格式
            if self.table_counter == 4:
                return f"""\\vspace{{-8pt}}
\\begin{{table}}[H]
\\captionsetup{{justification=raggedright,singlelinecheck=false}}
\\caption{{{self.escape_latex(table['caption'])}}}
\\vspace{{2pt}}
\\footnotesize
\\setlength{{\\tabcolsep}}{{3pt}}
\\renewcommand{{\\arraystretch}}{{0.85}}
\\begin{{tabular}}{{{col_spec}}}
\\toprule[0.5pt]
{table_content}
\\bottomrule[0.5pt]
\\end{{tabular}}
\\end{{table}}

"""
            else:
                return f"""\\vspace{{-8pt}}
\\begin{{table}}[H]
\\captionsetup{{justification=raggedright,singlelinecheck=false}}
\\caption{{{self.escape_latex(table['caption'])}}}
\\vspace{{3pt}}
\\small
\\begin{{tabular}}{{{col_spec}}}
\\toprule[0.5pt]
{table_content}
\\bottomrule[0.5pt]
\\end{{tabular}}
\\end{{table}}

"""

    def generate_text_latex(self, text_item: Dict) -> str:
        """生成文本的LaTeX代码"""
        text = self.escape_latex(text_item['text'])
        level = text_item['level']

        if level == 1:
            # 移除编号前缀，只保留标题文本
            clean_text = re.sub(r'^\d+\.?\s*', '', text)

            # 检查是否是无序号章节
            if clean_text.lower() in ['authors contributions', 'funding', 'institutional review board statement',
                                    'informed consent statement', 'data availability statement',
                                    'acknowledgments', 'conflict of interest']:
                return f"\\section*{{{clean_text}}}\n\n"
            else:
                # 使用有编号的章节以确保子章节编号正确
                section_numbers = {
                    'introduction': 1,
                    'conceptual framework': 2,
                    'methodology': 3,
                    'results': 4,
                    'discussion': 5,
                    'conclusion': 6
                }

                section_num = section_numbers.get(clean_text.lower(), None)
                if section_num:
                    # 设置章节计数器并生成有编号的章节
                    return f"\\setcounter{{section}}{{{section_num-1}}}\\section{{{clean_text}}}\n\n"
                elif clean_text.lower() == 'references':
                    return f"\\section*{{References}}\n\n"
                else:
                    return f"\\section{{{clean_text}}}\n\n"
        elif level == 2:
            clean_text = re.sub(r'^\d+\.\d+\.?\s*', '', text)
            return f"\\subsection{{{clean_text}}}\n\n"
        elif level == 3:
            clean_text = re.sub(r'^\d+\.\d+\.\d+\.?\s*', '', text)
            return f"\\subsubsection{{{clean_text}}}\n\n"
        else:
            # 普通段落 - 不加粗
            return f"{text}\n\n"

    def create_latex_template(self) -> str:
        """创建完整的LaTeX模板"""
        return f"""\\documentclass[11pt]{{article}} % 指定文档类型 [可以指定字号，纸张大小]{{文档类型包括article,book,beamer(英文文档),ctexart,ctexbook,ctexbeamer(中文文档)等}}
% 导言区: 全局设置, 宏包调用等。（以下部分是对全局有效果的区域）
%\\usepackage{{showframe}}  % 显示页面边框
\\usepackage{{graphicx}}
\\usepackage[paperwidth=210mm, paperheight=285mm, top=2.5cm, bottom=2.5cm, left=2cm, right=2cm]{{geometry}}%页面设置纸张类型和边距
\\usepackage{{fancyhdr}}%页眉页脚的宏包
\\usepackage[absolute, overlay]{{textpos}}
\\usepackage{{graphicx}}
\\usepackage{{subcaption}}
%\\usepackage{{luatexja-fontspec}}
%\\setmainjfont{{FandolSong}}

\\usepackage{{amsmath}} % AMS 数学公式 宏包
\\usepackage{{amssymb}} % AMS 数学符号 宏包
\\usepackage{{amsfonts}} % AMS 数学字体 宏包
\\usepackage{{amsthm}} % 数学
\\usepackage{{xcolor}}%颜色宏包
\\usepackage{{tcolorbox}} % 导入 tcolorbox 宏包，用于创建带颜色背景的框
\\usepackage{{multicol}} % 导入multicol宏包分栏！！
% \\usepackage{{microtype}}
\\usepackage{{etoolbox}}%参考文献自定义宏包
\\usepackage{{enumitem}}
\\usepackage{{ragged2e}} % 导入 ragged2e 宏包用于设置对齐
%字体相关宏包
\\usepackage{{titlesec}}%标题自定义宏包
\\usepackage{{fontspec}}
\\usepackage{{parskip}}
%链接到文献地址宏包
%修改参考文献的数字的格式的
\\usepackage[numbers,sort&compress]{{natbib}}
%第一段首行缩进
\\usepackage{{indentfirst}}

%++++++list 宏包
\\setitemize{{topsep=3pt,parsep=0pt,itemsep=0pt,leftmargin=*,labelsep=5.5mm,align=parleft}}
\\setenumerate{{topsep=3pt,parsep=0pt,itemsep=0pt,leftmargin=*,labelsep=5.5mm,align=parleft}}
\\setlist[description]{{itemsep=0mm}}

%表格宏包
\\usepackage{{placeins}}

\\usepackage{{makecell}}
\\usepackage{{caption}}
\\captionsetup{{labelsep=period}}
\\usepackage{{booktabs}}  % 用于创建漂亮的表格
\\usepackage[colorlinks,linkcolor=black,urlcolor=black,citecolor=black]{{hyperref}}
\\urlstyle{{same}}  %++++++为了网址颜色相同

\\usepackage{{url}} %++++++为了网址断行
\\def\\UrlBreaks{{\\do\\A\\do\\B\\do\\C\\do\\D\\do\\E\\do\\F\\do\\G\\do\\H\\do\\I\\do\\J
\\do\\K\\do\\L\\do\\M\\do\\N\\do\\O\\do\\P\\do\\Q\\do\\R\\do\\S\\do\\T\\do\\U\\do\\V
\\do\\W\\do\\X\\do\\Y\\do\\Z\\do\\[\\do\\\\\\do\\]\\do\\^\\do\\_\\do\\`\\do\\a\\do\\b
\\do\\c\\do\\d\\do\\e\\do\\f\\do\\g\\do\\h\\do\\i\\do\\j\\do\\k\\do\\l\\do\\m\\do\\n
\\do\\o\\do\\p\\do\\q\\do\\r\\do\\s\\do\\t\\do\\u\\do\\v\\do\\w\\do\\x\\do\\y\\do\\z
\\do\\.\\do\\@\\do\\\\\\do\\/\\do\\!\\do\\_\\do\\|\\do\\;\\do\\>\\do\\]\\do\\)\\do\\,
\\do\\?\\do\\'\\do+\\do\\=\\do\\#}}%++++++为了网址断行

\\usepackage{{tabularx}}%表格通栏显示的宏
\\usepackage{{multirow}}
\\usepackage{{float}} % 引入 float 宏包，以便使用 [H] 参数

\\renewcommand{{\\arraystretch}}{{1}}%表格的行距
\\newcolumntype{{C}}{{>{{\\centering\\arraybackslash}}X}} % 定义新的列类型 C

\\setlength{{\\abovecaptionskip}}{{3pt}} % 设置标题与表格上方的距离
\\setlength{{\\belowcaptionskip}}{{0pt}}  % 设置标题与表格下方的距离
\\usepackage[justification=centering]{{caption}}
\\captionsetup{{
    font={{footnotesize}}, % 设置标题字体为小号字体 (9pt)
    justification=centering, % 设置标题居中对齐
    labelfont=bf, % 设置标签字体为粗体
    textfont=normalfont  % 设置标题文字为粗体
}}
% \\usepackage{{capt-of}}  % 添加此宏包以支持多列环境中的caption设置 - 可能缺失，暂时注释
%环境

\\renewenvironment{{proof}}[1][\\proofname]{{\\par %% \\proofname allows to have "Proof of my theorem"
  \\pushQED{{\\qed}}%
  \\normalfont \\topsep6\\p@\\@plus6\\p@\\relax
  \\trivlist
  \\item[\\hskip\\labelsep
        \\bfseries %% "Proof" is bold
    #1\\@addpunct{{.}}]\\ignorespaces %% Period instead of colon
}}{{%
  \\popQED\\endtrivlist\\@endpefalse
}}

\\newcounter{{theorem}}
 \\setcounter{{theorem}}{{0}}
 \\newtheorem{{Theorem}}[theorem]{{Theorem}}

 \\newcounter{{lemma}}
 \\setcounter{{lemma}}{{0}}
 \\newtheorem{{Lemma}}[lemma]{{Lemma}}

 \\newcounter{{corollary}}
 \\setcounter{{corollary}}{{0}}
 \\newtheorem{{Corollary}}[corollary]{{Corollary}}

 \\newcounter{{proposition}}
 \\setcounter{{proposition}}{{0}}
 \\newtheorem{{Proposition}}[proposition]{{Proposition}}

 \\newcounter{{characterization}}
 \\setcounter{{characterization}}{{0}}
 \\newtheorem{{Characterization}}[characterization]{{Characterization}}

 \\newcounter{{property}}
 \\setcounter{{property}}{{0}}
 \\newtheorem{{Property}}[property]{{Property}}

 \\newcounter{{problem}}
 \\setcounter{{problem}}{{0}}
 \\newtheorem{{Problem}}[problem]{{Problem}}

 \\newcounter{{example}}
 \\setcounter{{example}}{{0}}
 \\newtheorem{{Example}}[example]{{Example}}

 \\newcounter{{examplesanddefinitions}}
 \\setcounter{{examplesanddefinitions}}{{0}}
 \\newtheorem{{ExamplesandDefinitions}}[examplesanddefinitions]{{Examples and Definitions}}

 \\newcounter{{remark}}
 \\setcounter{{remark}}{{0}}
 \\newtheorem{{Remark}}[remark]{{Remark}}

 \\newcounter{{definition}}
 \\setcounter{{definition}}{{0}}
 \\newtheorem{{Definition}}[definition]{{Definition}}

 \\newcounter{{hypothesis}}
 \\setcounter{{hypothesis}}{{0}}
 \\newtheorem{{Hypothesis}}[hypothesis]{{Hypothesis}}

 \\newcounter{{notation}}
 \\setcounter{{notation}}{{0}}
 \\newtheorem{{Notation}}[notation]{{Notation}}

 \\newcounter{{assumption}}
 \\setcounter{{assumption}}{{0}}
 \\newtheorem{{Assumption}}[assumption]{{Assumption}}

 \\newcounter{{algorithm}}
 \\setcounter{{algorithm}}{{0}}
 \\newtheorem{{Algorithm}}[algorithm]{{Algorithm}}

  % Define left/right mark in math environment
\\let\\originalleft\\left
\\let\\originalright\\right
\\renewcommand{{\\left}}{{\\mathopen{{}}\\mathclose\\bgroup\\originalleft}}
\\renewcommand{{\\right}}{{\\aftergroup\\egroup\\originalright}}

%定义proof样式
\\makeatletter
\\renewenvironment{{proof}}[1][\\proofname]{{\\par
  \\pushQED{{\\qed}}%
  \\normalfont\\topsep6\\p@\\@plus6\\p@\\relax
  \\trivlist
  \\item[\\hskip\\labelsep
        \\bfseries #1\\@addpunct{{.}}]\\ignorespaces
}}{{%
  \\popQED\\endtrivlist\\@endpefalse
}}

% 自定义参考文献字号10pt
\\apptocmd{{\\thebibliography}}{{\\small}}{{}}{{}}
%这是设置引用格式为上标格式的
\\setcitestyle{{super,open=,close=,citesep={{,}}}}
\\bibliographystyle{{plain}}
%2、定义全文字体为Times New Roman
\\setmainfont{{Times New Roman}}
%4 设置首行缩进为 2 字符
\\setlength{{\\parskip}}{{0pt}} % 设置段落之间的距离为0
\\setlength{{\\parindent}}{{2em}} % 必须在parskip之后设置以确保首行缩进生效

%自定义颜色
\\definecolor{{black}}{{HTML}}{{000000}} % 定义颜色
\\definecolor{{bgcolor}}{{HTML}}{{d8d8d8}} % 定义颜色
\\definecolor{{blue}}{{HTML}}{{1F497D}} % 定义颜色
\\definecolor{{red}}{{HTML}}{{FF0000}} % 定义颜色
% 自定义链接
\\newcommand{{\\ORCID}}{{https://www.baidu.com}}
%模板应用自定义样式
\\newcommand{{\\supernum}}[1]{{\\textsuperscript{{\\bfseries{{#1}}}}}}

%自定标题样式
 \\par\\vspace{{5pt}}%段前
\\renewcommand\\refname{{References}}
 \\par\\vspace{{0pt}} % 设置距离为10磅

%页眉设置开始------
% 首页的自定义页眉样式
% \\pagestyle{{fancy}}
\\fancypagestyle{{first}}{{%
  % 清除默认的页眉和页脚设置
  \\fancyhf{{}}
  \\renewcommand{{\\headrulewidth}}{{0.75pt}} % 移除页眉的线
  \\renewcommand{{\\headrule}}{{\\color{{black}}\\hrule width\\headwidth height \\headrulewidth}} % 设置页眉线颜色
  % \\setlength{{\\headheight}}{{25pt}} % 设置页眉高度
  % 设置页眉内容
  \\fancyhead[C]{{
    \\par\\vspace{{0.6pt}}%段前
    {{\\noindent\\fontsize{{9pt}}{{10pt}}\\selectfont{{ \\textit{{\\textbf{{Forum for Linguistic Studies}}}} |  Volume 07 | Issue 07 | July 2025}}}}%
    \\par\\vspace{{-12pt}}%段后
  }}

  \\setlength{{\\headsep}}{{4mm}} % 设置页眉线与正文之间的距离
  % 保留页脚的位置

  \\fancyfoot[C]{{\\thepage}}
  \\renewcommand{{\\footrulewidth}}{{0pt}} % 页脚的线
}}
%页眉公共样式
\\fancypagestyle{{other}}{{%
    \\fancyhf{{}}
    \\renewcommand{{\\headrulewidth}}{{0.75pt}} % 页眉的线
    \\renewcommand{{\\headrule}}{{\\color{{black}}\\hrule width\\headwidth height \\headrulewidth}} % 设置页眉线颜色
   % \\setlength{{\\headheight}}{{18pt}} % 设置页眉高度
    % 设置页眉内容
    \\fancyhead[C]{{\\par\\vspace{{0.6pt}}%段前
    {{\\noindent\\fontsize{{9pt}}{{10pt}}\\selectfont{{ \\textit{{\\textbf{{Forum for Linguistic Studies}}}} | Volume 07 | Issue 07 | July 2025}}}} \\par\\vspace{{-12pt}}%段后
    }}
     \\setlength{{\\headsep}}{{7mm}} % 设置页眉线与正文之间的距离
    % 设置页脚内容
    \\setcounter{{page}}{{1071}}%序号从特定值开始的时候加
    \\fancyfoot[C]{{\\thepage}}
    \\renewcommand{{\\footrulewidth}}{{0pt}} % 页脚的线
}}

%页眉设置结束---------

%文章类型
\\newcommand{{\\articletype}}[1]{{%
    \\par\\vspace{{9.55pt}}%段前
    {{\\noindent\\fontsize{{12pt}}{{19.2pt}}\\bf\\selectfont #1}}%
    \\par\\vspace{{10pt}}%段后
}}
%文章标题
\\newcommand{{\\articletitle}}[1]{{%
    \\par\\vspace{{0.2pt}}%段前
    \\noindent\\parbox{{\\textwidth}}{{\\raggedright\\fontsize{{16pt}}{{19.2pt}}\\bfseries #1}}% 居左对齐
    \\par\\vspace{{0pt}}%段后
}}
%文章标题
\\newcommand{{\\articleabstract}}[1]{{%
    \\par\\vspace{{10pt}}%段前
    \\noindent\\parbox{{\\textwidth}}{{\\centering\\fontsize{{12pt}}{{14.4pt}}\\bfseries #1}}% 居中对齐
    \\par\\vspace{{5pt}}%段后
}}

%以下是常用格式
% 定义第一级标题的格式
\\titleformat{{\\section}}
  {{\\fontsize{{15pt}}{{18}}\\bfseries\\selectfont}} % 设置字号为15pt，加粗
  {{\\thesection.}} % 标题编号格式
  {{0.5em}} % 标题编号和标题文字的距离
  {{}} % 可以在这里添加额外的格式设定，比如颜色等
% 设置 section 标题的段前和段后间距
\\titlespacing*{{\\section}}{{0pt}}{{15pt}}{{10pt}} % 标题文字与正文间距


% 定义第二级标题的格式
\\titleformat{{\\subsection}}
  {{\\color{{black}}\\bfseries\\fontsize{{12}}{{14.4}}\\selectfont}}
  {{\\thesubsection.}}
  {{0.5em}}
  {{}}
\\titlespacing*{{\\subsection}}{{0pt}}{{15pt}}{{10pt}}

% 定义第三级标题的格式
\\titleformat{{\\subsubsection}}
  {{\\color{{black}}\\bfseries\\fontsize{{12}}{{14.4}}\\selectfont}}
  {{\\thesubsubsection.}}
  {{0.5em}}
  {{}}
\\titlespacing*{{\\subsubsection}}{{0pt}}{{5pt}}{{5pt}}

\\newcommand{{\\subsubsubsection}}[1]{{%
    \\par\\vspace{{5pt}}%
    \\noindent{{\\color{{black}}\\fontsize{{12}}{{14.4}}\\selectfont #1}}%
    \\par\\vspace{{5pt}}%
}}
% 重新定义公式编号格式
\\renewcommand{{\\theequation}}{{\\arabic{{equation}}}}

\\begin{{document}}%{{这个括号中是环境名称}}

\\begin{{figure}}[b]
  \\begin{{tcolorbox}}[colback=bgcolor, colframe=gray!0!white, boxrule=0mm, arc=0mm, left=2mm, right=2mm, top=2mm, bottom=2mm]
    % \\par\\vspace{{5pt}}%
   \\fontsize{{8}}{{10}}\\selectfont{{\\color{{blue}}\\MakeUppercase{{*Corresponding Author:}}}}
    \\par\\vspace{{1.64pt}}%
    \\fontsize{{8}}{{10}}\\selectfont{{corresponding author name, corresponding author affiliation; Email: <EMAIL>}}\\par\\vspace{{10pt}}%
    \\fontsize{{8}}{{10}}\\selectfont{{\\color{{blue}}\\MakeUppercase{{ARTICLE INFO}}}}
    \\par\\vspace{{1.64pt}}%
    \\fontsize{{8}}{{10}}{{Received: Day Month Year | Revised: Day Month Year | Accepted: Day Month Year | Published Online: Day Month Year\\\\
    DOI: https://doi.org/10.30564/xxxx.vxix.xxxx}}\\par\\vspace{{10pt}}%
    \\fontsize{{8}}{{10}}\\selectfont{{\\color{{blue}}\\MakeUppercase{{CITATION}}}}
    \\par\\vspace{{1.64pt}}%
    \\fontsize{{8}}{{10}}{{Lastname, The initials of the firstname., Lastname, The initials of the firstname., Lastname, The initials of the firstname., et al., 2024. Title.
Journal Name. x(x): x–x. DOI: https://doi.org/10.30564/xxxx.vxix.xxxx}}\\par\\vspace{{10pt}}%
    \\fontsize{{8}}{{10}}\\selectfont{{\\color{{blue}}\\MakeUppercase{{Copyright}}}}
    \\par\\vspace{{1.64pt}}%
    \\fontsize{{8}}{{10}}{{Copyright © 2024 by the author(s). Published by Bilingual Publishing Group. This is an open access article under the Creative Commons Attribution-NonCommercial 4.0 International (CC BY-NC 4.0) License (https://creativecommons.org/licenses/by-nc/4.0/).}}%
  \\end{{tcolorbox}}
\\end{{figure}}

\\vspace{{12pt}}
\\noindent\\begin{{minipage}}[c]{{\\dimexpr3.7cm+20pt}} % 左边部分占据三分之一的宽度
 % \\vfill % 垂直填充
 % \\hspace{{0pt}} % 设置左侧间距为10pt
  \\includegraphics[width=3.7cm]{{logo.png}} % 插入图片
 % \\hspace{{0pt}} % 设置右侧间距为10pt
%  \\vfill % 垂直填充
\\end{{minipage}}
\\hfill % 用于分隔左右部分
\\hspace{{-22pt}}
\\noindent\\begin{{minipage}}[c]{{\\dimexpr\\textwidth-3.7cm\\relax}} % 右边部分占据剩余的宽度
 \\vspace{{2.7mm}}
\\begin{{tcolorbox}}[colback=bgcolor, colframe=gray!0!white, boxrule=0mm, arc=0mm, left=0mm, right=0mm, top=0mm, bottom=0mm, height=18mm] % 去掉所有边距
\\centering{{
     \\fontsize{{12pt}}{{14pt}}\\selectfont{{\\vspace{{8.55pt}}\\textbf{{Journal Name}}\\vspace{{7pt}}\\\\
      https://journals.bilpubgroup.com/xxxxxx\\vspace{{10pt}}}}
    }}
  \\end{{tcolorbox}}
  \\vspace{{-0.8mm}}
\\end{{minipage}}
{{\\noindent\\color{{black}}\\rule{{\\linewidth}}{{1pt}}}}

\\articletype{{ARTICLE}}
\\vspace{{6pt}}
\\articletitle{{{self.escape_latex(self.title)}}}
%作者
\\vspace{{3pt}} % 段前3磅
\\begin{{center}}%
 {{\\fontsize{{10pt}}{{12pt}}\\selectfont \\textbf{{\\textit{{Firstname Lastname \\supernum{{1}}\\href{{https://orcid.org/0000-0003-3203-8743}}{{\\includegraphics[width=0.46cm]{{ORCID.png}}}}, Firstname Lastname \\supernum{{2*}}\\href{{https://orcid.org/0000-0002-0145-5766}}{{\\includegraphics[width=0.46cm]{{ORCID.png}}}}}}}}}}
\\end{{center}}
\\vspace{{5pt}} % 段后5磅
%作者介绍
\\noindent\\fontsize{{10pt}}{{15pt}}\\selectfont\\textit{{\\supernum{{1}} University Department, University Name, City State ZIP/Zone, Country}}\\\\
\\noindent\\fontsize{{10pt}}{{15pt}}\\selectfont\\textit{{\\supernum{{2}} Group, Laboratory, City State ZIP/Zone, Country}}\\\\
{{\\noindent\\color{{black}}\\rule{{\\linewidth}}{{0.5pt}}}}
\\articleabstract{{ ABSTRACT }}
 \\fontsize{{10}}{{15}}\\selectfont{{{self.escape_latex(self.abstract)}}}\\\\
\\thispagestyle{{first}}
\\pagestyle{{other}}
{{\\noindent\\fontsize{{10}}{{15}}\\selectfont{{\\textit{{\\textbf{{Keywords: }}}}  {self.escape_latex(self.keywords)}}}}}
\\vspace{{9mm}}

\\begin{{multicols}}{{2}}

{{CONTENT}}

{{REFERENCES}}

\\end{{multicols}}

\\end{{document}}
"""

    def merge_content(self, text_content: List[Dict], images: List[Dict], tables: List[Dict]) -> tuple:
        """合并所有内容生成LaTeX，返回(主要内容, 参考文献内容)"""
        content_parts = []
        references_content = []

        # 重置计数器
        self.figure_counter = 0
        self.table_counter = 0

        # 过滤重复的章节内容
        text_content = self._filter_duplicate_sections(text_content)

        # 跟踪已插入的图片和表格
        inserted_images = set()
        inserted_tables = set()

        # 分离主要内容和参考文献内容
        in_references = False

        for text_item in text_content:
            # 检查是否到达References章节
            if text_item['level'] == 1 and 'references' in text_item['text'].lower():
                in_references = True
                continue  # 跳过References标题，因为会在参考文献生成中添加

            if in_references:
                # 收集参考文献内容
                references_content.append(text_item)
                continue

            # 添加主要内容
            content_parts.append(self.generate_text_latex(text_item))

            text = text_item['text']

            # 查找图片引用并插入对应的图片
            figure_match = re.search(r'(?:Figure|图|Fig\.?)[ ]*(\d+)', text, re.IGNORECASE)
            if figure_match:
                figure_num = int(figure_match.group(1))
                # 查找对应编号的图片
                for i, image in enumerate(images):
                    if i + 1 == figure_num and i not in inserted_images:
                        self.figure_counter += 1
                        content_parts.append(self.generate_figure_latex(image))
                        inserted_images.add(i)
                        break

            # 查找表格引用并插入对应的表格
            table_match = re.search(r'(?:Table|表|Tab\.?)[ ]*(\d+)', text, re.IGNORECASE)
            if table_match:
                table_num = int(table_match.group(1))
                # 查找对应编号的表格
                for i, table in enumerate(tables):
                    if i + 1 == table_num and i not in inserted_tables:
                        content_parts.append(self.generate_table_latex(table))
                        inserted_tables.add(i)
                        break

        # 插入剩余未插入的图片和表格
        for i, image in enumerate(images):
            if i not in inserted_images:
                self.figure_counter += 1
                content_parts.append(self.generate_figure_latex(image))

        for i, table in enumerate(tables):
            if i not in inserted_tables:
                content_parts.append(self.generate_table_latex(table))

        return "".join(content_parts), references_content

    def convert_to_latex(self, output_filename: str = "output.tex"):
        """转换为LaTeX文档"""
        try:
            # 加载文档
            if not self.load_document():
                return False

            # 提取元数据
            self.extract_metadata()

            # 创建logo文件
            self.create_logo_file()

            # 提取内容
            logger.info("🔍 开始提取文档内容...")
            images = self.extract_images()
            tables = self.extract_tables()
            text_content = self.extract_text_content()

            # 生成LaTeX内容
            logger.info("📝 生成LaTeX文档...")
            merged_content, references_content = self.merge_content(text_content, images, tables)

            # 生成参考文献
            references_latex = self.generate_references_latex(references_content)

            # 创建完整的LaTeX文档
            template = self.create_latex_template()
            latex_document = template.replace("{CONTENT}", merged_content)
            latex_document = latex_document.replace("{REFERENCES}", references_latex)

            # 保存LaTeX文件
            output_path = self.output_dir / self.output_file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(latex_document)

            logger.info(f"✅ LaTeX文件已生成: {output_path}")

            # 编译PDF
            pdf_success = self.compile_pdf(self.output_file)

            # 返回tex文件路径（无论PDF是否成功）
            return output_path

        except Exception as e:
            logger.error(f"❌ 转换过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def compile_pdf(self, tex_filename: str) -> bool:
        """编译LaTeX文件为PDF"""
        try:
            # 切换到输出目录
            original_cwd = os.getcwd()
            os.chdir(self.output_dir)

            # 运行XeLaTeX编译器
            cmd = ["xelatex", "-interaction=nonstopmode", tex_filename]
            logger.info(f"🔧 编译PDF: {' '.join(cmd)}")

            # 运行两次以确保交叉引用正确
            for i in range(2):
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      encoding='utf-8', errors='ignore', timeout=120)

                if result.returncode != 0:
                    logger.warning(f"⚠️ 编译警告 (第{i+1}次)")

                logger.info(f"✅ 编译完成 (第{i+1}次)")

            # 检查PDF是否生成
            pdf_file = Path(tex_filename).with_suffix('.pdf')
            if pdf_file.exists():
                logger.info(f"🎉 PDF生成成功：{self.output_dir / pdf_file}")
                return True
            else:
                logger.error(f"❌ PDF文件未生成: {pdf_file}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ 编译超时")
            return False
        except Exception as e:
            logger.error(f"❌ 编译过程中出错: {e}")
            return False
        finally:
            os.chdir(original_cwd)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="正确的DOCX到LaTeX转换器")
    parser.add_argument("input_file", help="输入的DOCX文档路径")
    parser.add_argument("-o", "--output", default="output_correct", help="输出目录")

    args = parser.parse_args()

    # 执行转换
    converter = CorrectDocxToLatexConverter(args.input_file, args.output)
    success = converter.convert_to_latex()

    if success:
        print(f"🎉 转换成功! 输出目录: {args.output}")
    else:
        print("❌ 转换失败!")
        sys.exit(1)


def compile_latex_to_pdf(tex_file: Path) -> bool:
    """编译LaTeX文件为PDF"""
    try:
        import subprocess
        import os

        # 切换到tex文件所在目录
        original_dir = os.getcwd()
        os.chdir(tex_file.parent)

        # 使用XeLaTeX编译（支持中文）
        cmd = ['xelatex', '-interaction=nonstopmode', tex_file.name]

        logger.info(f"🔄 正在编译PDF: {' '.join(cmd)}")

        # 运行两次以确保引用正确
        for i in range(2):
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                logger.error(f"❌ 编译失败 (第{i+1}次):")
                logger.error(result.stdout)
                logger.error(result.stderr)
                return False
            logger.info(f"✅ 第{i+1}次编译完成")

        # 检查PDF是否生成
        pdf_file = tex_file.with_suffix('.pdf')
        if pdf_file.exists():
            logger.info(f"🎉 PDF生成成功: {pdf_file}")
            return True
        else:
            logger.error("❌ PDF文件未生成")
            return False

    except Exception as e:
        logger.error(f"❌ 编译过程中出现错误: {e}")
        return False
    finally:
        # 恢复原始目录
        os.chdir(original_dir)


if __name__ == "__main__":
    main()
