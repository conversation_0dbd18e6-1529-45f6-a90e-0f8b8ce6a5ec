# LaTeX文档转换工具

## 项目概述

本项目是一个专业的DOCX到LaTeX转换工具，专门用于学术论文的格式转换。支持将Word文档转换为符合学术期刊要求的LaTeX格式，包括正确的引用格式、图表处理、参考文献等。

## 主要功能

### 1. 文档转换
- **DOCX到LaTeX转换**：完整转换Word文档为LaTeX格式
- **图片提取与处理**：自动提取并转换文档中的图片
- **表格转换**：将Word表格转换为LaTeX表格格式
- **参考文献处理**：自动生成参考文献列表

### 2. 格式规范
- **引用格式**：支持方括号数字格式 [1], [2] 等
- **字体设置**：Times New Roman字体
- **页面布局**：A4纸张，合适的页边距
- **段落格式**：首行缩进2字符，段落间距设置

### 3. 学术规范
- **标题层级**：正确的章节标题格式
- **图表编号**：自动图表编号和引用
- **交叉引用**：支持图表的交叉引用
- **参考文献样式**：符合学术期刊要求

## 今日工作记录

### 问题解决历程

#### 1. 引用格式问题修复
**问题描述**：
- 原始转换结果显示引用为上标格式（如¹, ²）
- 用户要求修改为方括号数字格式（如[1], [2]）

**解决方案**：
```latex
% 修改前（上标格式）
\setcitestyle{super,open=[,close=],citesep={, }}

% 修改后（方括号格式）
\setcitestyle{numbers,square,comma}
```

**技术细节**：
- 使用natbib包的`\setcitestyle`命令
- `numbers`：使用数字引用
- `square`：使用方括号
- `comma`：多个引用用逗号分隔

#### 2. 转换程序优化
**改进内容**：
- 修复了输出路径处理逻辑
- 改进了文件保存机制
- 优化了PDF编译流程

**代码修改**：
```python
# 构造函数改进
def __init__(self, docx_path: str, output_path: str = "output"):
    # 智能处理输出路径
    output_path_obj = Path(output_path)
    if output_path_obj.suffix == '.tex':
        self.output_dir = output_path_obj.parent
        self.output_file = output_path_obj.name
    else:
        self.output_dir = output_path_obj
        self.output_file = "output.tex"
```

### 测试验证

#### 测试文件
- 使用了`实例文档/单栏案例/TI 988 原始版本.docx`进行测试
- 成功生成了`test_citation_fixed/output.tex`和对应的PDF文件

#### 验证结果
- ✅ 引用格式正确显示为方括号数字格式
- ✅ PDF编译成功，无错误
- ✅ 文档结构完整，格式规范

## 使用方法

### 基本用法
```bash
python docx_to_latex_correct.py "输入文件.docx" -o "输出目录/文件名.tex"
```

### 示例
```bash
python docx_to_latex_correct.py "实例文档/单栏案例/TI 988 原始版本.docx" -o "test_citation_fixed/output.tex"
```

## 输出文件结构

转换完成后会生成以下文件：
```
输出目录/
├── output.tex          # 主LaTeX文件
├── output.pdf          # 编译生成的PDF文件
├── images/             # 图片目录
│   ├── figure1.png
│   ├── figure2.png
│   └── ...
├── logo.png           # 期刊logo
└── ORCID.png          # ORCID图标
```

## 技术特性

### LaTeX包依赖
- `natbib`：参考文献管理
- `fontspec`：字体设置
- `geometry`：页面布局
- `graphicx`：图片处理
- `booktabs`：表格美化
- `hyperref`：超链接支持

### 引用系统配置
```latex
\usepackage[numbers,sort&compress]{natbib}
\setcitestyle{numbers,square,comma}
\bibliographystyle{plain}
```

## 项目文件说明

- `docx_to_latex_correct.py`：主转换程序
- `template.tex`：LaTeX模板文件
- `实例文档/`：测试用的示例文档
- `test_citation_fixed/`：最新的测试输出目录

## 更新日志

### 2025-07-31
- ✅ 修复引用格式问题：从上标格式改为方括号数字格式
- ✅ 优化转换程序的文件路径处理逻辑
- ✅ 完成引用格式修复的测试验证
- ✅ 更新README文档，记录完整的工作过程

## 注意事项

1. **引用格式**：确保使用方括号数字格式 [1], [2]，而非上标格式
2. **文件路径**：支持中文路径和文件名
3. **图片处理**：自动提取并转换Word中的图片
4. **PDF编译**：需要安装XeLaTeX编译器

## 下一步计划

- [ ] 进一步优化表格转换算法
- [ ] 增加更多引用格式选项
- [ ] 改进图片质量和尺寸处理
- [ ] 添加更多学术期刊模板支持
